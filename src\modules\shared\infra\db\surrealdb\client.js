import Surreal, { ConnectionStatus } from "surrealdb";

const DEFAULT_CLIENT_NAME = "client";

/**
 * @type {Record<string, Surreal>}
 */
const db = {
	[DEFAULT_CLIENT_NAME]: null,
};

/**
 * @returns {Promise<Surreal>}
 */
async function connect({ clientName } = {}) {
	try {
		clientName = clientName || DEFAULT_CLIENT_NAME;

		const t0 = performance.now();

		const client = new Surreal();

		client.emitter.subscribe("connecting", () => {
			console.log("SurrealDB client connecting...");
		});

		client.emitter.subscribe("connected", () => {
			console.log("  connected");
		});

		// Connect to the database
		await client.connect(process.env.SURREALDB_URL);

		await client.ready;

		// Select a specific namespace / database for future queries
		await client.use({
			namespace: process.env.SURREALDB_NS,
			database: process.env.SURREALDB_DB,
		});

		// Signin as a namespace, database, or root user
		await client.signin({
			namespace: process.env.SURREALDB_NS,
			database: process.env.SURREALDB_DB,
			username: process.env.SURREALDB_USER,
			password: process.env.SURREALDB_PASSWORD,
		});

		// Get version and test if a query works
		const version = await client.version();
		const [testResult] = await client.query("1+1");

		if (testResult !== 2) {
			throw new Error("[ERR] SurrealDB client could not connect");
		}

		const totalTime = (performance.now() - t0).toFixed(2);
		console.log(`[OK] SurrealDB client connected (v${version}) in ${totalTime}ms`);

		db.status = client.status;

		db[clientName] = client;

		return client;
	} catch (_err) {
		console.error(_err.message);
	}
}

/**
 * @returns {Promise<Surreal>}
 */
export async function createSurrealdbClient({ clientName } = {}) {
	clientName = clientName || DEFAULT_CLIENT_NAME;

	return await connect({ clientName });
}

/**
 * @return {Surreal}
 */
export function getSurreal({ clientName } = {}) {
	clientName = clientName || DEFAULT_CLIENT_NAME;

	return db[clientName];
}

/**
 * @returns {keyof typeof import("surrealdb").ConnectionStatus}
 */
export function getSurrealStatus({ clientName } = {}) {
	clientName = clientName || DEFAULT_CLIENT_NAME;

	return db[clientName]?.status ?? ConnectionStatus.Error;
}
