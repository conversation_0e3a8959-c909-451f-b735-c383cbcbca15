import { getSurreal } from "../../shared/infra/db/surrealdb/client.js";
import { dbResultHandler } from "../../shared/infra/db/surrealdb/dbResultHandler.js";

export const usersRepository = {
	insertOne: async ({ email }) => {
		const response = await dbResultHandler.try(
			getSurreal().create("users", {
				email,
			}),
		);

		return dbResultHandler.findOne(response);
	},

	findOne: async ({ email }) => {
		const response = await dbResultHandler.try(
			getSurreal().query(
				/* surrealql */ `
					SELECT email
					FROM ONLY users
					WHERE email = $email
					LIMIT 1
				`,
				{
					email,
				},
			),
		);

		return dbResultHandler.findOne(response);
	},
};
