/**
 * @typedef {Object} ErrorCode
 * @property {string} [code]
 * @property {string} [description]
 */

/**
 * @param {Object} options
 * @param {string} options.entityId
 * @param {ErrorCode} [options.errorCode={}]
 */
export function buildResponseError({ entityId, errorCode = {} }) {
	const response = {
		entityId: entityId || "none",
		errorId: errorCode?.code || "UNKNOWN",
		message: errorCode?.description || errorCode?.code || "Unknown error",
	};

	return response;
}
