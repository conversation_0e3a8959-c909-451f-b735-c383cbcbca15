{"$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "indentStyle": "tab", "lineWidth": 160, "indentWidth": 4}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noParameterAssign": "off"}, "correctness": {"noUndeclaredVariables": "error", "noUnusedVariables": "error", "noUnusedImports": "error"}}}, "javascript": {"formatter": {"quoteStyle": "double", "trailingCommas": "all"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}