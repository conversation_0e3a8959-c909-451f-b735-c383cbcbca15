import { RATE_LIMIT } from "../../../config/rateLimit.js";
import { registerUser } from "./usersController.js";

/** @param {import("fastify").FastifyInstance} fastify */
export const usersRouter = async (fastify) => {
	await fastify.register(import("@fastify/rate-limit"), RATE_LIMIT.default);

	fastify.route({
		method: "POST",
		url: "/users/register",
		schema: registerUser.schema,
		handler: registerUser.handler,
	});
};
