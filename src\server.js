import path from "node:path";
import fastifyAutoload from "@fastify/autoload";
import { createSurrealdbClient } from "./modules/shared/infra/db/surrealdb/client.js";
import { publicRouter } from "./routes/v1/public/publicRouter.js";
import { initSchemas } from "./schemas/index.js";
import { env } from "./utils/common/env.js";

/**
 * @callback FastifyController
 * @param {import("fastify").FastifyRequest} req
 * @param {import("fastify").FastifyReply} res
 */

/**
 * @typedef {import("fastify").FastifySchema} FastifyRouteSchema
 */

/** @type {import("fastify").FastifyServerOptions} */
export const options = {
	ajv: {
		customOptions: {
			coerceTypes: true,
			removeAdditional: "failing",
		},
	},
};

export async function serviceApp(fastify, opts) {
	delete opts.skipOverride; // only serves testing purpose

	await createSurrealdbClient();

	// Load all external plugins defined in plugins/external
	// those should be registered first as your application plugins might depend on them
	await fastify.register(fastifyAutoload, {
		dir: path.join(import.meta.dirname, "plugins/external"),
		options: {},
	});

	// Load all your application plugins defined in plugins/app
	// those should be support plugins that are reused
	// through your application
	fastify.register(fastifyAutoload, {
		dir: path.join(import.meta.dirname, "plugins/app"),
		options: { ...opts },
	});

	// Load all plugins defined in routes
	// define your routes in one of these
	fastify.register(fastifyAutoload, {
		dir: path.join(import.meta.dirname, "routes"),
		autoHooks: true,
		cascadeHooks: true,
		options: { ...opts },
	});

	// Load schemas
	initSchemas(fastify);

	// Load routers
	fastify.register(publicRouter);

	fastify.setErrorHandler((err, request, reply) => {
		console.error(
			{
				err,
				request: {
					method: request.method,
					url: request.url,
					query: request.query,
					params: request.params,
				},
			},
			"Unhandled error occurred",
		);

		reply.code(err.statusCode ?? 500);

		let message = "Internal Server Error";

		if (err.statusCode && err.statusCode < 500) {
			message = err.message;
		}

		if (env.isLocal()) {
			message = err;
		}

		return { message };
	});

	// An attacker could search for valid URLs if your 404 error handling is not rate limited
	fastify.setNotFoundHandler({}, (request, reply) => {
		console.warn(
			{
				request: {
					method: request.method,
					url: request.url,
					query: request.query,
					params: request.params,
				},
			},
			"Resource not found",
		);

		reply.code(404);

		return { message: "Not Found" };
	});
}
