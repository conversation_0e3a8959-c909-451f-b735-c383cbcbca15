import { RecordId } from "surrealdb";

/**
 * Converts the given value to a RecordId.
 * @param {string | {tb: string; id: string}} value
 * @returns {RecordId | null}
 *
 * @example
 * toRecordId({ tb: "users", id: "123" });
 * toRecordId("users:123");
 */
export function toRecordId(value) {
	if (value === undefined || value === null) {
		return null;
	}

	if (typeof value === "string") {
		const [tb, id] = value.split(":"); // "users:123" --> ["users", "123"]
		return new RecordId(tb, id);
	}

	if (typeof value === "object") {
		if (value.tb === undefined || value.tb === null) {
			throw new Error(`Invalid RecordId.tb: ${JSON.stringify(value)}`);
		}
		if (value.id === undefined || value.id === null) {
			throw new Error(`Invalid RecordId.id: ${JSON.stringify(value)}`);
		}
		return new RecordId(value.tb, value.id);
	}

	throw new Error(`Invalid RecordId: ${JSON.stringify(value)}`);
}
