// @ts-check

import { buildResponseError } from "../../../utils/common/buildResponseError.js";
import { DB_ERROR_IDS } from "../../shared/infra/db/surrealdb/dbResultHandler.js";
import { usersRepository } from "./usersRepository.js";

export const registerUser = {
	/** @type {import("../../../server.js").FastifyRouteSchema} */
	schema: {
		body: {
			type: "object",
			required: ["email"],
			properties: {
				email: {
					$ref: "http://pop.app/users#/properties/email",
				},
			},
		},
	},
	/** @type {import("../../../server.js").FastifyController} */
	handler: async (req, res) => {
		// @ts-ignore
		const { email } = req.body;

		const { success, errorId } = await usersRepository.insertOne({ email });

		if (!success) {
			res.status(errorId === DB_ERROR_IDS.alreadyExists ? 409 : 500);

			return buildResponseError({
				entityId: "users",
				errorCode: {
					code: errorId,
					description: "User already exists",
				},
			});
		}

		return {
			message: "User created",
		};
	},
};
