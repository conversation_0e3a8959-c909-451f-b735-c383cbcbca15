export class CharacterEntity {
	static fields = ["id", "user", "name"];

	constructor(payload) {
		for (const field of CharacterEntity.fields) {
			if (!payload[field]) {
				throw new Error(`Character must have a ${field}`);
			}
		}

		this.id = payload.id;
		this.user = payload.user;
		this.name = payload.name;
	}

	static build({ id, user, name }) {
		return new CharacterEntity({ id, user, name });
	}
}
