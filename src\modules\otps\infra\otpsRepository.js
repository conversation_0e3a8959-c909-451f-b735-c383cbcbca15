import { getSurreal } from "../../../shared/infra/db/surrealdb/client.js";
import { dbResultHandler } from "../../../shared/infra/db/surrealdb/dbResultHandler.js";

export const otpsRepository = {
	insertOne: async ({ email, code }) => {
		const response = await dbResultHandler.try(
			getSurreal().create("otps", {
				email,
				code,
			}),
		);

		return dbResultHandler.findOne(response);
	},

	findOne: async ({ code }) => {
		const response = await dbResultHandler.try(
			getSurreal().query(
				/* surrealql */ `
					SELECT code
					FROM ONLY otps
					WHERE code = $code
					LIMIT 1
				`,
				{
					code,
				},
			),
		);

		return dbResultHandler.findOne(response);
	},
};
