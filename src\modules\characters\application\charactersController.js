// @ts-check

import { buildResponseError } from "../../../utils/common/buildResponseError.js";
import { DB_ERROR_IDS } from "../../shared/infra/db/surrealdb/dbResultHandler.js";
import { charactersRepository } from "../infra/repositories/characterRepository.js";
import { usersRepository } from "../infra/usersRepository.js";

export const createCharacters = {
	/** @type {import("../../../server.js").FastifyRouteSchema} */
	schema: {
		body: {
			type: "object",
			required: ["userId"],
			properties: {
				userId: {
					$ref: "http://pop.app/users#/properties/id",
				},
				name: {
					type: "string",
					minLength: 3,
					maxLength: 64,
				},
			},
		},
	},
	/** @type {import("../../../server.js").FastifyController} */
	handler: async (req, res) => {
		// @ts-ignore
		const { userId, name } = req.body;

		const { success, errorId } = await charactersRepository.insertOne({});

		if (!success) {
			res.status(errorId === DB_ERROR_IDS.alreadyExists ? 409 : 500);

			return buildResponseError({
				entityId: "characters",
				errorCode: {
					code: errorId,
					description: "User already exists",
				},
			});
		}

		return {
			message: "User created",
		};
	},
};
