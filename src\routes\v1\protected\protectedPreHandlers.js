import { getTokenFromHeader, verifyJwt } from "../../../modules/shared/infra/auth/authService.js";

// https://www.fastify.io/docs/latest/Reference/Hooks/#manage-errors-from-a-hook
export function verifyAndDecodeJwt(req, res, next) {
	const token = getTokenFromHeader(req.headers.authorization);
	if (!token) {
		res.status(401);
		return next(
			new Error("Missing token", {
				cause: {
					errorCode: "JWT_MISSING",
					message: "Missing token",
				},
			}),
		);
	}

	const { isValid, /*message,*/ decoded } = verifyJwt(token);

	if (!isValid) {
		res.status(401);
		next(
			new Error("Invalid JWT", {
				cause: {
					errorCode: "JWT_INVALID",
					message: "Invalid JWT",
				},
			}),
		);
	}

	decoded.password = undefined;
	req.user = decoded;

	next();
}
