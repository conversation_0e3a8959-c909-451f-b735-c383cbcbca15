import * as common from "../modules/shared/domain/commonSchemas.js";
import * as user from "../modules/users/domain/schemas/userSchema.js";

function addMultipleSchemas(fastifyInstance, schemas) {
	const arrSchemas = Object.values(schemas);
	if (arrSchemas.length === 0) {
		throw new Error("No schemas found");
	}

	for (const _schema of arrSchemas) {
		fastifyInstance.addSchema(_schema);
	}
}

/**
 * @param {import("fastify").FastifyInstance} fastifyInstance
 */
export function initSchemas(fastifyInstance) {
	const t0 = performance.now();

	addMultipleSchemas(fastifyInstance, common);
	addMultipleSchemas(fastifyInstance, user);

	const loadTimeMs = (performance.now() - t0).toFixed(2);

	// Count schemas
	const schemaCount = Object.keys(fastifyInstance.getSchemas()).length;
	console.log(`[OK] Loaded ${schemaCount} schemas in ${loadTimeMs}ms`);
}
