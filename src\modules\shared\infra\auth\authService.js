import jwt from "jsonwebtoken";
import { customAlphabet } from "nanoid";
import { cleanEmail } from "../../domain/emailService.js";
import { getSurreal } from "../db/surrealdb/client.js";

/**
 * @param {{id: string, username: string, email: string}}
 * @returns {string} JWT
 *
 * @example
 * generateJWT({id...})
 */
export function generateJWT({ id, username, email }) {
	if (!id) return null;

	const payload = {
		id,
		username,
		email: cleanEmail(email),
		iat: Date.now(),
	};

	const token = jwt.sign(payload, process.env.JWT_SECRET, {
		algorithm: "HS256",
		expiresIn: "7d",
	});

	return token;
}

/**
 *
 * @param {string} authorization
 * @returns {string} token
 *
 * @example
 * getToken("Bearer 1234") // "1234"
 */
export function getTokenFromHeader(authorization) {
	if (!authorization) return "";
	const fragments = authorization.split(" ");
	let token = "";

	if (fragments[0] === "Bearer") {
		token = fragments[1];
	}

	return token;
}

/**
 * @typedef {Object} VerifyResult
 * @property {boolean} isValid
 * @property {*} decoded Si es válido, contiene el payload del token.
 * @property {string} message Si es inválido, mensaje de error.
 * @property {string} errorName Si es inválido, nombre del error.
 */

/**
 * Verifica un token JWT.
 * @param {string} token
 * @returns {VerifyResult}
 */
export function verifyJwt(token) {
	return jwt.verify(token, process.env.JWT_SECRET, (err, decoded) => {
		if (err) {
			return {
				isValid: false,
				message: err.message,
				errorName: err.name,
			};
		}

		return {
			isValid: true,
			message: "Token válido",
			decoded,
		};
	});
}

export function generateMagicLinkCode() {
	const alphabet = "0123456789";

	const code = customAlphabet(alphabet, 6)();

	return code;
}

/**
 * @param {Object} params
 * @param {string} params.code
 * @return {Promise<Array<{}>>}
 */
export async function verifyMagicLink({ code }) {
	const [[deletedRecord]] = await getSurreal().query(
		/* surrealql */ `
			DELETE magicLink
			WHERE code = $code
			RETURN BEFORE
		`,
		{
			code,
		},
	);

	if (!deletedRecord) {
		return {
			success: false,
			errorCode: "INVALID_MAGIC_LINK_CODE",
		};
	}

	return {
		success: true,
		deletedRecord,
	};
}
