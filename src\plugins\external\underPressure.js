import fastifyUnderPressure from "@fastify/under-pressure";
import fp from "fastify-plugin";
import { ConnectionStatus } from "surrealdb";
import { getSurrealStatus } from "../../modules/shared/infra/db/surrealdb/client.js";

/**
 * @param {import("fastify").FastifyInstance} fastify
 */
export const autoConfig = (fastify) => {
	return {
		maxEventLoopDelay: 1_000,
		maxHeapUsedBytes: 100_000_000,
		maxRssBytes: 1_000_000_000,
		maxEventLoopUtilization: 0.98,
		message: "The server is under pressure, retry later!",
		retryAfter: 50,
		healthCheck: async () => {
			try {
				return getSurrealStatus() === ConnectionStatus.Connected;
			} catch (err) {
				fastify.log.error(err, "healthCheck has failed");
				throw new Error("Database connection is not available");
			}
		},
		healthCheckInterval: 5000,
	};
};

/**
 * A Fastify plugin for mesuring process load and automatically
 * handle of "Service Unavailable"
 *
 * @see {@link https://github.com/fastify/under-pressure}
 *
 * Video on the topic: Do not thrash the event loop
 * @see {@link https://www.youtube.com/watch?v=VI29mUA8n9w}
 */
export default fp(fastifyUnderPressure, {
	// dependencies: [],
});
