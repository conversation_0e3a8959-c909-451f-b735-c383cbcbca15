import { getSurreal } from "../../../shared/infra/db/surrealdb/client.js";
import { dbResultHandler } from "../../../shared/infra/db/surrealdb/dbResultHandler.js";
import { CharacterEntity } from "../../domain/entities/characterEntity.js";

export const charactersRepository = {
	/**
	 * @param {Record<typeof CharacterEntity.fields, unknown>} payload
	 */
	insertOne: async (payload) => {
		const response = await dbResultHandler.try(getSurreal().create("characters", payload));

		return dbResultHandler.findOne(response);
	},

	findMany: async (userId) => {
		const response = await dbResultHandler.try(
			getSurreal().query(
				/* surrealql */ `
					SELECT *
					FROM characters
					WHERE user = $userId
				`,
				{
					userId,
				},
			),
		);

		return dbResultHandler.findMany(response);
	},
};
