// @ts-check

export const DB_ERROR_IDS = {
	generic: "GENERIC",
	alreadyExists: "ALREADY_EXISTS",
};

/**
 * @typedef DbResponse
 * @property {boolean} success
 * @property {any} result
 * @property {any} error
 * @property {typeof DB_ERROR_IDS[keyof typeof DB_ERROR_IDS]} errorId
 */

/**
 * @param {Object} options
 * @param {any} options.result
 * @param {any} options.error
 * @param {typeof DB_ERROR_IDS[keyof typeof DB_ERROR_IDS]} options.errorId
 *
 * @returns {DbResponse}
 */
function buildResponse({ result, error, errorId }) {
	return {
		success: Bo<PERSON>an(result),
		result,
		error,
		errorId: errorId ?? DB_ERROR_IDS.generic,
	};
}

/**
 * @typedef {DbResponse & {result?: unknown}} FindOneReturn
 */

/**
 * @typedef {DbResponse & {result: Array<unknown>}} FindManyReturn
 */

export const dbResultHandler = {
	try: async (fnc) => {
		try {
			if (fnc.then) {
				return await fnc;
			}

			return await fnc();
		} catch (error) {
			let errorId = DB_ERROR_IDS.generic;

			if (error.message.includes("already contains")) {
				errorId = DB_ERROR_IDS.alreadyExists;
			}

			return buildResponse({
				result: null,
				error: error?.message ?? error,
				errorId,
			});
		}
	},

	/**
	 * @param {DbResponse} response
	 * @returns {FindOneReturn}
	 */
	findOne: (response) => {
		return buildResponse({
			...response,
			result: response?.[0],
		});
	},

	/**
	 * @param {DbResponse} response
	 * @returns {FindManyReturn}
	 */
	findMany: (response) => {
		return buildResponse(response);
	},
};
