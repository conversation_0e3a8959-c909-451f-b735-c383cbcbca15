import closeWithGrace from "close-with-grace";
import Fastify from "fastify";
import fastifyPlugin from "fastify-plugin";

// Import your application as a normal plugin.
import { options, serviceApp } from "./server.js";

function getLoggerOptions() {
	// Only if the program is running in an interactive terminal
	if (process.stdout.isTTY) {
		return {
			level: "info",
			transport: {
				target: "pino-pretty",
				options: {
					translateTime: "HH:MM:ss Z",
					ignore: "pid,hostname",
				},
			},
		};
	}

	return { level: process.env.LOG_LEVEL ?? "silent" };
}

const app = Fastify({
	...options,
	logger: getLoggerOptions(),
});

async function init() {
	// Register your application as a normal plugin.
	// fp must be used to override default error handler
	app.register(fastifyPlugin(serviceApp));

	closeWithGrace(
		{
			delay: 500,
		},
		async ({ err }) => {
			if (err != null) {
				console.error(err);
			}

			await app.close();
		},
	);

	await app.ready();

	try {
		const port = process.env.PORT ?? 3000;

		app.listen({ port });

		console.log(`[OK] Server listening on port ${port}`);
	} catch (err) {
		console.error(err);
		process.exit(1);
	}
}

init();
