{"name": "pop-a4-api", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"start:dev": "node --env-file-if-exists=.env --watch src/app.js | pino-pretty", "start:prod": "node --env-file-if-exists=.env src/app.js", "lint": "biome check", "lint:fix": "biome check --write"}, "keywords": [], "author": "Icaruk", "license": "ISC", "packageManager": "pnpm@10.13.1", "dependencies": {"@fastify/autoload": "^6.3.1", "@fastify/cors": "^11.0.1", "@fastify/helmet": "^13.0.1", "@fastify/rate-limit": "^10.3.0", "@fastify/under-pressure": "^9.0.3", "bcryptjs": "^3.0.2", "close-with-grace": "^2.2.0", "fastify": "^5.4.0", "fastify-plugin": "^5.0.1", "jsonwebtoken": "^9.0.2", "nanoid": "^5.1.5", "pino-pretty": "^13.0.0", "surrealdb": "^1.3.2"}, "devDependencies": {"@biomejs/biome": "2.1.2"}}